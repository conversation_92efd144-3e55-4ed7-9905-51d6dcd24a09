'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Upload, HardDrive, Image as ImageIcon, Settings } from 'lucide-react';
import { settingsService } from '@/lib/services';
import { StudioSettings } from '@/types';
import toast from 'react-hot-toast';

interface UploadSettingsTabProps {
  settings: StudioSettings;
  onUpdate: (settings: StudioSettings) => void;
}

interface UploadSettingsData {
  maxFileSize: number;
  allowedFormats: string;
  autoResize: boolean;
  storageQuota: number;
}

export default function UploadSettingsTab({ settings, onUpdate }: UploadSettingsTabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<UploadSettingsData>({
    defaultValues: {
      maxFileSize: settings.maxFileSize,
      allowedFormats: settings.allowedFormats,
      autoResize: settings.autoResize,
      storageQuota: settings.storageQuota,
    },
  });

  const watchAutoResize = watch('autoResize');

  const onSubmit = async (data: UploadSettingsData) => {
    setIsLoading(true);
    console.log('Upload settings data:', data);
    console.log('Data types:', {
      maxFileSize: typeof data.maxFileSize,
      storageQuota: typeof data.storageQuota
    });
    try {
      const updatedSettings = await settingsService.updateUploadSettings(data);
      onUpdate(updatedSettings);
      toast.success('Upload settings updated successfully');
    } catch (error: any) {
      console.error('Upload settings error:', error);
      toast.error(error.response?.data?.message || 'Failed to update upload settings');
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (mb: number) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  const getFormatList = () => {
    return settings.allowedFormats.split(',').map(format => format.trim().toUpperCase());
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Upload className="h-6 w-6 text-primary-600 mr-2" />
        <h2 className="text-xl font-semibold text-gray-900">Upload Settings</h2>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* File Size Settings */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <HardDrive className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">File Size Limits</h3>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum File Size (MB)
              </label>
              <select
                {...register('maxFileSize', {
                  required: 'Maximum file size is required',
                  min: { value: 1, message: 'Minimum file size is 1 MB' },
                  max: { value: 100, message: 'Maximum file size is 100 MB' },
                  valueAsNumber: true,
                })}
                className="input-field"
              >
                <option value={5}>5 MB</option>
                <option value={10}>10 MB</option>
                <option value={15}>15 MB</option>
                <option value={20}>20 MB</option>
                <option value={25}>25 MB</option>
                <option value={50}>50 MB</option>
                <option value={100}>100 MB</option>
              </select>
              {errors.maxFileSize && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.maxFileSize.message}
                </p>
              )}
              <p className="mt-1 text-sm text-gray-600">
                Maximum size allowed for individual image uploads
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Storage Quota (MB)
              </label>
              <select
                {...register('storageQuota', {
                  required: 'Storage quota is required',
                  min: { value: 100, message: 'Minimum storage quota is 100 MB' },
                  max: { value: 10000, message: 'Maximum storage quota is 10000 MB' },
                  valueAsNumber: true,
                })}
                className="input-field"
              >
                <option value={500}>500 MB</option>
                <option value={1000}>1 GB</option>
                <option value={2000}>2 GB</option>
                <option value={5000}>5 GB</option>
                <option value={10000}>10 GB</option>
              </select>
              {errors.storageQuota && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.storageQuota.message}
                </p>
              )}
              <p className="mt-1 text-sm text-gray-600">
                Total storage space available for all images
              </p>
            </div>
          </div>
        </div>

        {/* File Format Settings */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <ImageIcon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Supported File Formats</h3>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Allowed Formats
              </label>
              <input
                {...register('allowedFormats', {
                  required: 'Allowed formats are required',
                })}
                className="input-field"
                placeholder="jpg,jpeg,png,gif,webp"
              />
              {errors.allowedFormats && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.allowedFormats.message}
                </p>
              )}
              <p className="mt-1 text-sm text-gray-600">
                Comma-separated list of allowed file extensions (without dots)
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Currently Supported Formats</h4>
              <div className="flex flex-wrap gap-2">
                {getFormatList().map((format) => (
                  <span
                    key={format}
                    className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded"
                  >
                    {format}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Image Processing Settings */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Settings className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Image Processing</h3>
          </div>

          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Auto-resize Images</h4>
                <p className="text-sm text-gray-600">
                  Automatically resize large images to optimize storage and loading
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  {...register('autoResize')}
                  type="checkbox"
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {watchAutoResize && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-green-900 mb-2">Auto-resize Benefits</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• Reduces storage usage</li>
                  <li>• Faster image loading for clients</li>
                  <li>• Maintains image quality</li>
                  <li>• Optimizes bandwidth usage</li>
                </ul>
              </div>
            )}
          </div>
        </div>

        {/* Current Settings Summary */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Current Settings Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Max File Size:</span>
                <span className="font-medium text-gray-900">{settings.maxFileSize} MB</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Storage Quota:</span>
                <span className="font-medium text-gray-900">{formatFileSize(settings.storageQuota)}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Auto-resize:</span>
                <span className={`font-medium ${settings.autoResize ? 'text-green-600' : 'text-red-600'}`}>
                  {settings.autoResize ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Formats:</span>
                <span className="font-medium text-gray-900">{getFormatList().length} types</span>
              </div>
            </div>
          </div>
        </div>

        {/* Storage Usage Warning */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start">
            <HardDrive className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-900">Storage Management Tips</h4>
              <ul className="text-sm text-yellow-800 mt-1 space-y-1">
                <li>• Monitor your storage usage regularly</li>
                <li>• Enable auto-resize to save space</li>
                <li>• Remove old client galleries when no longer needed</li>
                <li>• Consider upgrading storage quota if needed</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4 border-t border-gray-200">
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary disabled:opacity-50"
          >
            {isLoading ? 'Updating...' : 'Update Upload Settings'}
          </button>
        </div>
      </form>
    </div>
  );
}
