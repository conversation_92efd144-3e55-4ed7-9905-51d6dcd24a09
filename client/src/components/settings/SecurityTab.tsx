'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Lock, Shield, Clock, Eye, EyeOff } from 'lucide-react';
import { settingsService } from '@/lib/services';
import { StudioSettings } from '@/types';
import toast from 'react-hot-toast';

interface SecurityTabProps {
  settings: StudioSettings;
  onUpdate: (settings: StudioSettings) => void;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface SecuritySettingsData {
  twoFactorEnabled: boolean;
  sessionTimeout: number;
}

export default function SecurityTab({ settings, onUpdate }: SecurityTabProps) {
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const passwordForm = useForm<PasswordFormData>();
  const settingsForm = useForm<SecuritySettingsData>({
    defaultValues: {
      twoFactorEnabled: settings.twoFactorEnabled,
      sessionTimeout: settings.sessionTimeout,
    },
  });

  const onPasswordSubmit = async (data: PasswordFormData) => {
    setIsChangingPassword(true);
    try {
      await settingsService.changePassword(
        data.currentPassword,
        data.newPassword,
        data.confirmPassword
      );
      passwordForm.reset();
      toast.success('Password changed successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to change password');
    } finally {
      setIsChangingPassword(false);
    }
  };

  const onSecuritySettingsSubmit = async (data: SecuritySettingsData) => {
    setIsUpdatingSettings(true);
    try {
      const updatedSettings = await settingsService.updateSecuritySettings(data);
      onUpdate(updatedSettings);
      toast.success('Security settings updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update security settings');
    } finally {
      setIsUpdatingSettings(false);
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Lock className="h-6 w-6 text-primary-600 mr-2" />
        <h2 className="text-xl font-semibold text-gray-900">Security Settings</h2>
      </div>

      <div className="space-y-8">
        {/* Change Password Section */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Shield className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Change Password</h3>
          </div>

          <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Password *
              </label>
              <div className="relative">
                <input
                  {...passwordForm.register('currentPassword', {
                    required: 'Current password is required',
                  })}
                  type={showPasswords.current ? 'text' : 'password'}
                  className="input-field pr-10"
                  placeholder="Enter current password"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('current')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPasswords.current ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {passwordForm.formState.errors.currentPassword && (
                <p className="mt-1 text-sm text-red-600">
                  {passwordForm.formState.errors.currentPassword.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                New Password *
              </label>
              <div className="relative">
                <input
                  {...passwordForm.register('newPassword', {
                    required: 'New password is required',
                    minLength: { value: 6, message: 'Password must be at least 6 characters' },
                  })}
                  type={showPasswords.new ? 'text' : 'password'}
                  className="input-field pr-10"
                  placeholder="Enter new password"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('new')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPasswords.new ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {passwordForm.formState.errors.newPassword && (
                <p className="mt-1 text-sm text-red-600">
                  {passwordForm.formState.errors.newPassword.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Confirm New Password *
              </label>
              <div className="relative">
                <input
                  {...passwordForm.register('confirmPassword', {
                    required: 'Please confirm your new password',
                    validate: (value) => {
                      const newPassword = passwordForm.getValues('newPassword');
                      return value === newPassword || 'Passwords do not match';
                    },
                  })}
                  type={showPasswords.confirm ? 'text' : 'password'}
                  className="input-field pr-10"
                  placeholder="Confirm new password"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('confirm')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPasswords.confirm ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {passwordForm.formState.errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">
                  {passwordForm.formState.errors.confirmPassword.message}
                </p>
              )}
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isChangingPassword}
                className="btn-primary disabled:opacity-50"
              >
                {isChangingPassword ? 'Changing...' : 'Change Password'}
              </button>
            </div>
          </form>
        </div>

        {/* Security Settings Section */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Clock className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Security Preferences</h3>
          </div>

          <form onSubmit={settingsForm.handleSubmit(onSecuritySettingsSubmit)} className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                <p className="text-sm text-gray-600">
                  Add an extra layer of security to your account
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  {...settingsForm.register('twoFactorEnabled')}
                  type="checkbox"
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Session Timeout (minutes)
              </label>
              <select
                {...settingsForm.register('sessionTimeout', {
                  required: 'Session timeout is required',
                  min: { value: 5, message: 'Minimum timeout is 5 minutes' },
                  max: { value: 480, message: 'Maximum timeout is 480 minutes' },
                  valueAsNumber: true,
                })}
                className="input-field"
              >
                <option value={15}>15 minutes</option>
                <option value={30}>30 minutes</option>
                <option value={60}>1 hour</option>
                <option value={120}>2 hours</option>
                <option value={240}>4 hours</option>
                <option value={480}>8 hours</option>
              </select>
              {settingsForm.formState.errors.sessionTimeout && (
                <p className="mt-1 text-sm text-red-600">
                  {settingsForm.formState.errors.sessionTimeout.message}
                </p>
              )}
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isUpdatingSettings}
                className="btn-primary disabled:opacity-50"
              >
                {isUpdatingSettings ? 'Updating...' : 'Update Settings'}
              </button>
            </div>
          </form>
        </div>

        {/* Security Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Security Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Use a strong password with at least 8 characters</li>
            <li>• Include uppercase, lowercase, numbers, and special characters</li>
            <li>• Don't reuse passwords from other accounts</li>
            <li>• Enable two-factor authentication for extra security</li>
            <li>• Log out from shared or public computers</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
