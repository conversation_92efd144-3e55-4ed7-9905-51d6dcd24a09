'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { User, Upload, Camera } from 'lucide-react';
import { settingsService } from '@/lib/services';
import { StudioProfile } from '@/types';
import toast from 'react-hot-toast';

interface ProfileTabProps {
  profile: StudioProfile;
  onUpdate: (profile: StudioProfile) => void;
}

interface ProfileFormData {
  name: string;
  description: string;
  phone: string;
  address: string;
}

export default function ProfileTab({ profile, onUpdate }: ProfileTabProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingPicture, setIsUploadingPicture] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileFormData>({
    defaultValues: {
      name: profile.name,
      description: profile.description || '',
      phone: profile.phone || '',
      address: profile.address || '',
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    setIsLoading(true);
    try {
      const updatedProfile = await settingsService.updateStudioProfile(data);
      onUpdate(updatedProfile);
      toast.success('Profile updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProfilePictureUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size should be less than 5MB');
      return;
    }

    setIsUploadingPicture(true);
    try {
      const result = await settingsService.uploadProfilePicture(file);
      const updatedSettings = await settingsService.updateProfileSettings({
        profilePicture: result.url,
      });
      
      // Update profile with new settings
      const updatedProfile = { ...profile, settings: updatedSettings };
      onUpdate(updatedProfile);
      toast.success('Profile picture updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to upload profile picture');
    } finally {
      setIsUploadingPicture(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <User className="h-6 w-6 text-primary-600 mr-2" />
        <h2 className="text-xl font-semibold text-gray-900">Profile Information</h2>
      </div>

      <div className="space-y-8">
        {/* Profile Picture Section */}
        <div className="flex items-center space-x-6">
          <div className="relative">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
              {profile.settings.profilePicture ? (
                <img
                  src={profile.settings.profilePicture}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <User className="h-12 w-12 text-gray-400" />
              )}
            </div>
            {isUploadingPicture && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              </div>
            )}
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900">{profile.name}</h3>
            <p className="text-sm text-gray-600">{profile.email}</p>
            <div className="mt-2">
              <label className="btn-secondary cursor-pointer inline-flex items-center space-x-2">
                <Upload className="h-4 w-4" />
                <span>{isUploadingPicture ? 'Uploading...' : 'Change Picture'}</span>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleProfilePictureUpload}
                  className="hidden"
                  disabled={isUploadingPicture}
                />
              </label>
            </div>
          </div>
        </div>

        {/* Profile Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Studio Name *
              </label>
              <input
                {...register('name', {
                  required: 'Studio name is required',
                  minLength: { value: 2, message: 'Name must be at least 2 characters' },
                })}
                className="input-field"
                placeholder="Enter studio name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input
                {...register('phone')}
                type="tel"
                className="input-field"
                placeholder="Enter phone number"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Studio Address
            </label>
            <input
              {...register('address')}
              className="input-field"
              placeholder="Enter studio address"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              {...register('description')}
              rows={4}
              className="input-field"
              placeholder="Tell us about your studio..."
            />
          </div>

          <div className="flex justify-end pt-4 border-t border-gray-200">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary disabled:opacity-50"
            >
              {isLoading ? 'Updating...' : 'Update Profile'}
            </button>
          </div>
        </form>

        {/* Account Information */}
        <div className="pt-6 border-t border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
          <div className="bg-gray-50 p-4 rounded-lg space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Email:</span>
              <span className="text-sm font-medium text-gray-900">{profile.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Account Created:</span>
              <span className="text-sm font-medium text-gray-900">
                {new Date(profile.createdAt).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Last Updated:</span>
              <span className="text-sm font-medium text-gray-900">
                {new Date(profile.updatedAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
