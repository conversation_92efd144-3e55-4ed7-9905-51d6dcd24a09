import { LoginRequest, AuthResponse, Image, ApiResponse } from '@/types';
import api from './api';

export const authService = {
  async login(data: LoginRequest): Promise<AuthResponse> {
    console.log('API call to:', '/clients/login', 'with data:', data);
    const response = await api.post<ApiResponse<AuthResponse>>('/clients/login', data);
    console.log('API response:', response.data);
    return response.data.data!;
  },
};

export const imageService = {
  async getMyImages(): Promise<Image[]> {
    const response = await api.get<ApiResponse<Image[]>>('/client-portal/my-images');
    return response.data.data || [];
  },

  async downloadAllImages(): Promise<void> {
    const response = await api.get('/client-portal/download-all', {
      responseType: 'blob',
    });
    
    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'my-images.zip');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  },

  getImageUrl(imagePath: string): string {
    // Simple and clean: always use relative path from backend
    return `http://localhost:5000/${imagePath}`;
  },
};
