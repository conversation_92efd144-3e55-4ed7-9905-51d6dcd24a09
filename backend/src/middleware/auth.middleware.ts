import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import config from '../config/env';

interface JwtPayload {
  id: string;
  role: 'ADMIN' | 'STUDIO' | 'USER';
  email: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      throw new ApiError(401, 'Access denied. No token provided.');
    }

    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
    req.user = decoded;

    // Verify user still exists in database
    let userExists = false;

    if (decoded.role === 'ADMIN') {
      userExists = !!(await prisma.admin.findUnique({ where: { id: decoded.id } }));
    } else if (decoded.role === 'STUDIO') {
      userExists = !!(await prisma.studio.findUnique({ where: { id: decoded.id } }));
    } else if (decoded.role === 'USER') {
      userExists = !!(await prisma.user.findUnique({ where: { id: decoded.id } }));
    } else if (decoded.role === 'CLIENT') {
      userExists = !!(await prisma.client.findUnique({ where: { id: decoded.id } }));
    }

    if (!userExists) {
      throw new ApiError(401, 'Token is no longer valid.');
    }

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new ApiError(401, 'Invalid token.'));
    } else {
      next(error);
    }
  }
};

export const authorize = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new ApiError(401, 'Access denied. User not authenticated.'));
    }

    if (!roles.includes(req.user.role)) {
      return next(new ApiError(403, 'Access denied. Insufficient permissions.'));
    }

    next();
  };
};

export const generateToken = (payload: JwtPayload): string => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn
  } as jwt.SignOptions);
};
