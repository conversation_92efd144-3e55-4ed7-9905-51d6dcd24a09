import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import bcrypt from 'bcryptjs';

export class SettingsService {
  // Get or create studio settings
  static async getStudioSettings(studioId: string) {
    let settings = await prisma.studioSettings.findUnique({
      where: { studioId },
    });

    // Create default settings if not exists
    if (!settings) {
      settings = await prisma.studioSettings.create({
        data: { studioId },
      });
    }

    return settings;
  }

  // Update profile settings
  static async updateProfile(studioId: string, data: {
    profilePicture?: string;
    businessAddress?: string;
    operatingHours?: string;
    socialLinks?: any;
    logoUrl?: string;
  }) {
    const settings = await this.getStudioSettings(studioId);

    return await prisma.studioSettings.update({
      where: { id: settings.id },
      data,
    });
  }

  // Update security settings
  static async updateSecurity(studioId: string, data: {
    twoFactorEnabled?: boolean;
    sessionTimeout?: number;
  }) {
    const settings = await this.getStudioSettings(studioId);

    // Ensure numeric fields are properly converted
    const updateData = {
      ...data,
      ...(data.sessionTimeout !== undefined && { sessionTimeout: Number(data.sessionTimeout) }),
    };

    return await prisma.studioSettings.update({
      where: { id: settings.id },
      data: updateData,
    });
  }

  // Update client settings
  static async updateClientSettings(studioId: string, data: {
    defaultAccessDuration?: number;
    autoGeneratePassword?: boolean;
    emailNotifications?: boolean;
    smsNotifications?: boolean;
  }) {
    const settings = await this.getStudioSettings(studioId);

    // Ensure numeric fields are properly converted
    const updateData = {
      ...data,
      ...(data.defaultAccessDuration !== undefined && { defaultAccessDuration: Number(data.defaultAccessDuration) }),
    };

    return await prisma.studioSettings.update({
      where: { id: settings.id },
      data: updateData,
    });
  }

  // Update upload settings
  static async updateUploadSettings(studioId: string, data: {
    maxFileSize?: number;
    allowedFormats?: string;
    autoResize?: boolean;
    storageQuota?: number;
  }) {
    const settings = await this.getStudioSettings(studioId);

    // Ensure numeric fields are properly converted
    const updateData = {
      ...data,
      ...(data.maxFileSize !== undefined && { maxFileSize: Number(data.maxFileSize) }),
      ...(data.storageQuota !== undefined && { storageQuota: Number(data.storageQuota) }),
    };

    return await prisma.studioSettings.update({
      where: { id: settings.id },
      data: updateData,
    });
  }

  // Get studio profile
  static async getStudioProfile(studioId: string) {
    const studio = await prisma.studio.findUnique({
      where: { id: studioId },
      select: {
        id: true,
        email: true,
        name: true,
        description: true,
        phone: true,
        address: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!studio) {
      throw new ApiError(404, 'Studio not found');
    }

    const settings = await this.getStudioSettings(studioId);

    return {
      ...studio,
      settings,
    };
  }
}

// Functional exports
export const getStudioSettings = async (studioId: string) => {
  let settings = await prisma.studioSettings.findUnique({
    where: { studioId },
  });

  // Create default settings if not exists
  if (!settings) {
    settings = await prisma.studioSettings.create({
      data: { studioId },
    });
  }

  return settings;
};

export const getStudioProfile = async (studioId: string) => {
  const studio = await prisma.studio.findUnique({
    where: { id: studioId },
    select: {
      id: true,
      email: true,
      name: true,
      description: true,
      phone: true,
      address: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!studio) {
    throw new ApiError(404, 'Studio not found');
  }

  const settings = await getStudioSettings(studioId);

  return {
    ...studio,
    settings,
  };
};

  // Update studio profile
  static async updateStudioProfile(studioId: string, data: {
    name?: string;
    description?: string;
    phone?: string;
    address?: string;
  }) {
    const studio = await prisma.studio.findUnique({
      where: { id: studioId },
    });

    if (!studio) {
      throw new ApiError(404, 'Studio not found');
    }

    return await prisma.studio.update({
      where: { id: studioId },
      data,
    });
  }

  // Change password
  static async changePassword(studioId: string, currentPassword: string, newPassword: string) {
    const studio = await prisma.studio.findUnique({
      where: { id: studioId },
    });

    if (!studio) {
      throw new ApiError(404, 'Studio not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, studio.password);
    if (!isCurrentPasswordValid) {
      throw new ApiError(400, 'Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    await prisma.studio.update({
      where: { id: studioId },
      data: { password: hashedNewPassword },
    });

    return { message: 'Password changed successfully' };
  }

  // Get dashboard statistics
  static async getDashboardStats(studioId: string) {
    const [
      totalClients,
      activeClients,
      totalImages,
      recentClients,
      recentImages,
      storageUsed
    ] = await Promise.all([
      prisma.client.count({ where: { studioId } }),
      prisma.client.count({ where: { studioId, isActive: true } }),
      prisma.image.count({ where: { studioId } }),
      prisma.client.count({
        where: {
          studioId,
          createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        }
      }),
      prisma.image.count({
        where: {
          studioId,
          uploadedAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        }
      }),
      prisma.image.aggregate({
        where: { studioId },
        _sum: { size: true }
      })
    ]);

    return {
      totalClients,
      activeClients,
      totalImages,
      recentClients,
      recentImages,
      storageUsed: storageUsed._sum.size || 0,
    };
  }

  // Get recent activity
  static async getRecentActivity(studioId: string, limit: number = 10) {
    const [recentClients, recentImages] = await Promise.all([
      prisma.client.findMany({
        where: { studioId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          name: true,
          uniqueId: true,
          createdAt: true,
          lastLogin: true,
        },
      }),
      prisma.image.findMany({
        where: { studioId },
        orderBy: { uploadedAt: 'desc' },
        take: limit,
        include: {
          client: {
            select: {
              name: true,
              uniqueId: true,
            },
          },
        },
      }),
    ]);

    return {
      recentClients,
      recentImages,
    };
  }
}
