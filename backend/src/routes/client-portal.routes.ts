import { Router } from 'express';
import * as clientController from '../controllers/client.controller';
import { authenticate } from '../middleware/auth.middleware';

const router = Router();

// Client portal routes (protected - client access only)
router.use(authenticate);

// Client's own images
router.get('/my-images', clientController.getMyImages);
router.get('/download-all', clientController.downloadAllImages);

export default router;
