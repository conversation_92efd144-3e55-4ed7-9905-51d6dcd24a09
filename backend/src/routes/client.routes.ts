import { Router } from 'express';
import * as clientController from '../controllers/client.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';

const router = Router();

// Studio routes (protected - studio access only)
router.use(authenticate);
router.use(authorize('STUDIO'));

// Basic client management routes (working)
router.post('/', clientController.createClient);
router.get('/', clientController.getStudioClients);
router.get('/:clientId', clientController.getClientById);

export default router;
