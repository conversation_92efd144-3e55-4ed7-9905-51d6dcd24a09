import { Router, Request, Response } from 'express';
import * as settingsController from '../controllers/settings.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { uploadProfilePicture, uploadLogo, handleUploadError } from '../middleware/upload.middleware';

const router = Router();

// Studio settings routes (protected - studio access only)
router.use(authenticate);
router.use(authorize('STUDIO'));

// Profile management
router.get('/profile', settingsController.getStudioProfile);
router.put('/profile-settings', settingsController.updateProfileSettings);

// Settings management
router.get('/studio-settings', settingsController.getStudioProfile);
router.put('/security-settings', settingsController.updateSecuritySettings);
router.put('/client-settings', settingsController.updateClientSettings);
router.put('/upload-settings', settingsController.updateUploadSettings);

// Password management
router.post('/change-password', settingsController.changePassword);

// Dashboard data
router.get('/dashboard-stats', settingsController.getDashboardStats);
router.get('/recent-activity', settingsController.getRecentActivity);

// File uploads
router.post('/upload-profile-picture', uploadProfilePicture, handleUploadError, (req: Request, res: Response): void => {
  if (!req.file) {
    res.status(400).json({
      success: false,
      message: 'No file uploaded',
    });
    return;
  }

  res.status(200).json({
    success: true,
    message: 'Profile picture uploaded successfully',
    data: {
      filename: req.file.filename,
      path: req.file.path,
      url: `http://localhost:5000/uploads/images/${req.file.filename}`,
    },
  });
});

router.post('/upload-logo', uploadLogo, handleUploadError, (req: Request, res: Response): void => {
  if (!req.file) {
    res.status(400).json({
      success: false,
      message: 'No file uploaded',
    });
    return;
  }

  res.status(200).json({
    success: true,
    message: 'Logo uploaded successfully',
    data: {
      filename: req.file.filename,
      path: req.file.path,
      url: `http://localhost:5000/uploads/images/${req.file.filename}`,
    },
  });
});

export default router;
