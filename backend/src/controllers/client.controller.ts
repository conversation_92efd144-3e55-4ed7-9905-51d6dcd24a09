import { Request, Response, NextFunction } from 'express';
import * as clientService from '../services/client.service';
import * as imageService from '../services/image.service';
import { ApiError } from '../utils/apiError';
import {
  createClientSchema,
  updateClientSchema,
  clientLoginSchema,
  bulkUploadSchema,
  updateImageSchema,
} from '../validations/client.validation';

// Create new client
export const createClient = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = createClientSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const client = await clientService.createClient(req.user.id, req.body);

      res.status(201).json({
        success: true,
        message: 'Client created successfully',
        data: client,
      });
    } catch (error) {
      next(error);
    }
};

// Get all clients for studio
export const getStudioClients = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const clients = await clientService.getStudioClients(req.user.id);

      res.status(200).json({
        success: true,
        message: 'Clients retrieved successfully',
        data: clients,
      });
    } catch (error) {
      next(error);
    }
};

// Get client by ID
export const getClientById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const client = await clientService.getClientById(clientId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'Client retrieved successfully',
      data: client,
    });
  } catch (error) {
    next(error);
  }
};

// Update client
export const updateClient = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = updateClientSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const { clientId } = req.params;
      const client = await clientService.updateClient(clientId, req.user.id, req.body);

      res.status(200).json({
        success: true,
        message: 'Client updated successfully',
        data: client,
      });
    } catch (error) {
      next(error);
    }
  }

// Reset client password
export const resetClientPassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const result = await clientService.resetClientPassword(clientId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'Password reset successfully',
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

// Regenerate QR code
export const regenerateQRCode = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const client = await clientService.regenerateQRCode(clientId, req.user.id);

      res.status(200).json({
        success: true,
        message: 'QR code regenerated successfully',
        data: client,
      });
    } catch (error) {
      next(error);
    }
};

// Delete client
export const deleteClient = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const { clientId } = req.params;
      const result = await clientService.deleteClient(clientId, req.user.id);

      res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
};

// Bulk upload images
export const bulkUploadImages = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = bulkUploadSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        throw new ApiError(400, 'No files uploaded');
      }

      const { clientId, descriptions } = req.body;
      const files = req.files as Express.Multer.File[];

      const images = await imageService.uploadImages(
        req.user.id,
        clientId,
        files,
        descriptions
      );

      res.status(201).json({
        success: true,
        message: 'Images uploaded successfully',
        data: images,
      });
    } catch (error) {
      next(error);
    }
};

// Get client images
export const getClientImages = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const { clientId } = req.params;
      const images = await imageService.getClientImages(req.user.id, clientId);

      res.status(200).json({
        success: true,
        message: 'Images retrieved successfully',
        data: images,
      });
    } catch (error) {
      next(error);
    }
};

// Update image
export const updateImage = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = updateImageSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const { imageId } = req.params;
      const image = await imageService.updateImage(imageId, req.user.id, req.body);

      res.status(200).json({
        success: true,
        message: 'Image updated successfully',
        data: image,
      });
    } catch (error) {
      next(error);
    }
};

// Delete image
export const deleteImage = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const { imageId } = req.params;
      const result = await imageService.deleteImage(imageId, req.user.id);

      res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
};

// Client login
export const clientLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = clientLoginSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      const { uniqueId, password } = req.body;
      const result = await clientService.clientLogin(uniqueId, password);

      res.status(200).json({
        success: true,
        message: 'Login successful',
        data: result,
      });
    } catch (error) {
      next(error);
    }
};

// Get client's own images (for client portal)
export const getMyImages = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const images = await imageService.getMyImages(req.user.id);

      res.status(200).json({
        success: true,
        message: 'Images retrieved successfully',
        data: images,
      });
    } catch (error) {
      next(error);
    }
};

// Download all images as ZIP
export const downloadAllImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const zipPath = await imageService.createClientZip(req.user.id);

    res.download(zipPath, (err) => {
      if (err) {
        console.error('Download error:', err);
      }
      // Clean up temp file
      try {
        require('fs').unlinkSync(zipPath);
      } catch (cleanupError) {
        console.error('Cleanup error:', cleanupError);
      }
    });
  } catch (error) {
    next(error);
  }
};
