import { Request, Response, NextFunction } from 'express';
import { SettingsService } from '../services/settings.service';
import { ApiError } from '../utils/apiError';
import {
  updateProfileSchema,
  updateProfileSettingsSchema,
  updateSecuritySettingsSchema,
  updateClientSettingsSchema,
  updateUploadSettingsSchema,
  changePasswordSchema,
} from '../validations/settings.validation';

// Get studio profile
export const getStudioProfile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const profile = await SettingsService.getStudioProfile(req.user.id);

      res.status(200).json({
        success: true,
        message: 'Profile retrieved successfully',
        data: profile,
      });
    } catch (error) {
      next(error);
    }
};

// Update studio profile
export const updateStudioProfile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = updateProfileSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const profile = await SettingsService.updateStudioProfile(req.user.id, req.body);

      res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        data: profile,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get studio settings
  static async getStudioSettings(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const settings = await SettingsService.getStudioSettings(req.user.id);

      res.status(200).json({
        success: true,
        message: 'Settings retrieved successfully',
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update profile settings
  static async updateProfileSettings(req: Request, res: Response, next: NextFunction) {
    try {
      const { error } = updateProfileSettingsSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const settings = await SettingsService.updateProfile(req.user.id, req.body);

      res.status(200).json({
        success: true,
        message: 'Profile settings updated successfully',
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update security settings
  static async updateSecuritySettings(req: Request, res: Response, next: NextFunction) {
    try {
      const { error } = updateSecuritySettingsSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const settings = await SettingsService.updateSecurity(req.user.id, req.body);

      res.status(200).json({
        success: true,
        message: 'Security settings updated successfully',
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update client settings
  static async updateClientSettings(req: Request, res: Response, next: NextFunction) {
    try {
      const { error } = updateClientSettingsSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const settings = await SettingsService.updateClientSettings(req.user.id, req.body);

      res.status(200).json({
        success: true,
        message: 'Client settings updated successfully',
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update upload settings
  static async updateUploadSettings(req: Request, res: Response, next: NextFunction) {
    try {
      const { error } = updateUploadSettingsSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const settings = await SettingsService.updateUploadSettings(req.user.id, req.body);

      res.status(200).json({
        success: true,
        message: 'Upload settings updated successfully',
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  }

  // Change password
  static async changePassword(req: Request, res: Response, next: NextFunction) {
    try {
      const { error } = changePasswordSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const { currentPassword, newPassword } = req.body;
      const result = await SettingsService.changePassword(req.user.id, currentPassword, newPassword);

      res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get dashboard statistics
  static async getDashboardStats(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const stats = await SettingsService.getDashboardStats(req.user.id);

      res.status(200).json({
        success: true,
        message: 'Dashboard statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get recent activity
  static async getRecentActivity(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const activity = await SettingsService.getRecentActivity(req.user.id);

      res.status(200).json({
        success: true,
        message: 'Recent activity retrieved successfully',
        data: activity,
      });
    } catch (error) {
      next(error);
    }
  }
}
