const { PrismaClient } = require('@prisma/client');
const path = require('path');

async function fixImagePaths() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 Fixing image paths in database...');
    
    // Get all images with wrong paths
    const images = await prisma.image.findMany();
    
    console.log(`Found ${images.length} images to check`);
    
    let fixedCount = 0;
    
    for (const image of images) {
      let newPath = image.path;
      
      // If path contains absolute path, fix it
      if (newPath.includes('/Users/') || newPath.includes('\\Users\\')) {
        // Extract just the filename
        const filename = path.basename(newPath);
        newPath = `uploads/images/${filename}`;
        
        // Update in database
        await prisma.image.update({
          where: { id: image.id },
          data: { path: newPath }
        });
        
        console.log(`✅ Fixed: ${image.originalName}`);
        console.log(`   Old: ${image.path}`);
        console.log(`   New: ${newPath}`);
        fixedCount++;
      } else if (!newPath.startsWith('uploads/')) {
        // If path doesn't start with uploads/, fix it
        const filename = path.basename(newPath);
        newPath = `uploads/images/${filename}`;
        
        await prisma.image.update({
          where: { id: image.id },
          data: { path: newPath }
        });
        
        console.log(`✅ Fixed: ${image.originalName}`);
        console.log(`   Old: ${image.path}`);
        console.log(`   New: ${newPath}`);
        fixedCount++;
      }
    }
    
    console.log(`\n🎉 Fixed ${fixedCount} image paths!`);
    
    // Show sample of fixed paths
    const updatedImages = await prisma.image.findMany({
      take: 5,
      orderBy: { uploadedAt: 'desc' }
    });
    
    console.log('\n📋 Sample updated paths:');
    updatedImages.forEach(img => {
      console.log(`- ${img.originalName}: ${img.path}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

fixImagePaths();
