# Photo Cap Application

A comprehensive photo management platform with admin panel, studio management, and user authentication system.

## 🏗️ Architecture

The application consists of three main components:

- **Backend** - Node.js + Express + TypeScript + Prisma + PostgreSQL
- **Admin Panel** - Next.js 14 (Port 3000) - For admin management
- **Client Panel** - Next.js 14 (Port 3001) - For studios and users

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn

### 1. Database Setup

Create a PostgreSQL database and update the connection string in `backend/.env`:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/photo_cap_db?schema=public"
```

### 2. Backend Setup

```bash
cd backend
npm install
npm run db:generate
npm run db:push
npm run db:seed
npm run dev
```

The backend will run on `http://localhost:5000`

### 3. Admin Panel Setup

```bash
cd admin
npm install
npm run dev
```

The admin panel will run on `http://localhost:3000`

### 4. Client Panel Setup

```bash
cd client
npm install
npm run dev
```

The client panel will run on `http://localhost:3001`

## 🔐 Default Credentials

### Admin Login
- **URL**: http://localhost:3000/login
- **Email**: <EMAIL>
- **Password**: admin123

### Demo Studio Login
- **URL**: http://localhost:3001/login
- **Email**: <EMAIL>
- **Password**: studio123

## 📋 Features

### Admin Panel Features
- ✅ Admin authentication
- ✅ Dashboard with statistics
- ✅ Studio management (approve/reject)
- ✅ User management
- ✅ Responsive sidebar navigation

### Studio Panel Features
- ✅ Studio registration
- ✅ Studio authentication
- ✅ Studio dashboard
- ✅ User management
- ✅ Settings page
- ✅ Responsive sidebar navigation

### Backend Features
- ✅ JWT authentication
- ✅ Role-based access control
- ✅ RESTful API endpoints
- ✅ Database seeding
- ✅ Input validation
- ✅ Error handling

## 🗄️ Database Schema

### Models
- **Admin** - System administrators
- **Studio** - Photography studios (pending/approved/rejected)
- **User** - End users associated with studios

### Relationships
- Studio → Users (One-to-Many)
- User → Studio (Many-to-One, optional)

## 🛠️ API Endpoints

### Authentication
- `POST /api/users/admin/login` - Admin login
- `POST /api/users/studio/register` - Studio registration
- `POST /api/users/studio/login` - Studio login

### Admin Routes (Protected)
- `GET /api/users/admin/studios` - Get all studios
- `PATCH /api/users/admin/studios/:id/status` - Update studio status
- `GET /api/users/admin/users` - Get all users

### Studio Routes (Protected)
- `GET /api/users/studio/users` - Get studio users

## 🎨 UI Components

Both admin and client panels use:
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **React Hook Form** for form handling
- **React Hot Toast** for notifications
- **Responsive design** with mobile support

## 📁 Project Structure

```
photo-cap/
├── backend/           # Node.js API server
│   ├── src/
│   │   ├── config/    # Configuration files
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── utils/
│   │   └── validations/
│   └── prisma/        # Database schema & migrations
├── admin/             # Admin panel (Next.js)
│   └── src/
│       ├── app/       # App router pages
│       ├── components/
│       ├── lib/       # API & auth utilities
│       └── types/
└── client/            # Studio panel (Next.js)
    └── src/
        ├── app/       # App router pages
        ├── components/
        ├── lib/       # API & auth utilities
        └── types/
```

## 🔧 Development

### Backend Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Run migrations
npm run db:seed      # Seed database
```

### Frontend Commands (Admin/Client)
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

## 🚀 Deployment

1. Set up PostgreSQL database
2. Configure environment variables
3. Build and deploy backend
4. Build and deploy frontend applications
5. Run database migrations and seeding

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
