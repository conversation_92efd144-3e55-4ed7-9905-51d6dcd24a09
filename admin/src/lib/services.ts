import { Studio, User, ApiResponse, CreateStudioRequest, UpdateStudioRequest } from '@/types';
import api from './api';

export const studioService = {
  async getAllStudios(): Promise<Studio[]> {
    const response = await api.get<ApiResponse<Studio[]>>('/users/admin/studios');
    return response.data.data || [];
  },

  async getStudioById(studioId: string): Promise<Studio> {
    const response = await api.get<ApiResponse<Studio>>(`/users/admin/studios/${studioId}`);
    return response.data.data!;
  },

  async createStudio(data: CreateStudioRequest): Promise<Studio> {
    const response = await api.post<ApiResponse<Studio>>('/users/admin/studios', data);
    return response.data.data!;
  },

  async updateStudio(studioId: string, data: UpdateStudioRequest): Promise<Studio> {
    const response = await api.put<ApiResponse<Studio>>(`/users/admin/studios/${studioId}`, data);
    return response.data.data!;
  },

  async deleteStudio(studioId: string): Promise<void> {
    await api.delete(`/users/admin/studios/${studioId}`);
  },

  async updateStudioStatus(studioId: string, status: 'APPROVED' | 'REJECTED'): Promise<Studio> {
    const response = await api.patch<ApiResponse<Studio>>(
      `/users/admin/studios/${studioId}/status`,
      { status }
    );
    return response.data.data!;
  },
};

export const userService = {
  async getAllUsers(): Promise<User[]> {
    const response = await api.get<ApiResponse<User[]>>('/users/admin/users');
    return response.data.data || [];
  },
};
