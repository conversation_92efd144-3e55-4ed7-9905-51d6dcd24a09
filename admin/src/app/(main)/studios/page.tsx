'use client';

import { useEffect, useState } from 'react';
import { Building2, Mail, Phone, MapPin, Calendar, Plus, Edit, Trash2, Eye } from 'lucide-react';
import { studioService } from '@/lib/services';
import { Studio } from '@/types';
import toast from 'react-hot-toast';
import CreateStudioModal from '@/components/CreateStudioModal';
import EditStudioModal from '@/components/EditStudioModal';
import ViewStudioModal from '@/components/ViewStudioModal';
import DeleteConfirmModal from '@/components/DeleteConfirmModal';

export default function StudiosPage() {
  const [studios, setStudios] = useState<Studio[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'>('ALL');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedStudio, setSelectedStudio] = useState<Studio | null>(null);

  useEffect(() => {
    fetchStudios();
  }, []);

  const fetchStudios = async () => {
    try {
      const data = await studioService.getAllStudios();
      setStudios(data);
    } catch (error) {
      toast.error('Failed to fetch studios');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStudioAction = async (studioId: string, status: 'APPROVED' | 'REJECTED') => {
    try {
      await studioService.updateStudioStatus(studioId, status);
      toast.success(`Studio ${status.toLowerCase()} successfully`);
      fetchStudios();
    } catch (error) {
      toast.error(`Failed to ${status.toLowerCase()} studio`);
    }
  };

  const handleCreateStudio = () => {
    setShowCreateModal(true);
  };

  const handleEditStudio = (studio: Studio) => {
    setSelectedStudio(studio);
    setShowEditModal(true);
  };

  const handleViewStudio = (studio: Studio) => {
    setSelectedStudio(studio);
    setShowViewModal(true);
  };

  const handleDeleteStudio = (studio: Studio) => {
    setSelectedStudio(studio);
    setShowDeleteModal(true);
  };

  const handleStudioCreated = () => {
    setShowCreateModal(false);
    fetchStudios();
    toast.success('Studio created successfully');
  };

  const handleStudioUpdated = () => {
    setShowEditModal(false);
    setSelectedStudio(null);
    fetchStudios();
    toast.success('Studio updated successfully');
  };

  const handleStudioDeleted = async () => {
    if (!selectedStudio) return;

    try {
      await studioService.deleteStudio(selectedStudio.id);
      setShowDeleteModal(false);
      setSelectedStudio(null);
      fetchStudios();
      toast.success('Studio deleted successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to delete studio');
    }
  };

  const filteredStudios = studios.filter(studio => {
    if (filter === 'ALL') return true;
    return studio.status === filter;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Studios Management</h1>
          <p className="text-gray-600">Manage studio registrations and approvals</p>
        </div>

        <div className="flex items-center space-x-4">
          <button
            onClick={handleCreateStudio}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Studio</span>
          </button>

          <div className="flex space-x-2">
            {['ALL', 'PENDING', 'APPROVED', 'REJECTED'].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {status}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredStudios.map((studio) => (
          <div key={studio.id} className="card p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className="bg-primary-100 p-2 rounded-full">
                  <Building2 className="h-5 w-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">{studio.name}</h3>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    studio.status === 'APPROVED' 
                      ? 'bg-green-100 text-green-800'
                      : studio.status === 'PENDING'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {studio.status}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3 mb-4">
              <div className="flex items-center text-sm text-gray-600">
                <Mail className="h-4 w-4 mr-2" />
                {studio.email}
              </div>
              
              {studio.phone && (
                <div className="flex items-center text-sm text-gray-600">
                  <Phone className="h-4 w-4 mr-2" />
                  {studio.phone}
                </div>
              )}
              
              {studio.address && (
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  {studio.address}
                </div>
              )}
              
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                {new Date(studio.createdAt).toLocaleDateString()}
              </div>
            </div>

            {studio.description && (
              <p className="text-sm text-gray-600 mb-4">{studio.description}</p>
            )}

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">
                {studio._count?.users || 0} users
              </span>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleViewStudio(studio)}
                  className="p-1 text-blue-600 hover:text-blue-800"
                  title="View Details"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEditStudio(studio)}
                  className="p-1 text-green-600 hover:text-green-800"
                  title="Edit Studio"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteStudio(studio)}
                  className="p-1 text-red-600 hover:text-red-800"
                  title="Delete Studio"
                >
                  <Trash2 className="h-4 w-4" />
                </button>

                {studio.status === 'PENDING' && (
                  <>
                    <button
                      onClick={() => handleStudioAction(studio.id, 'APPROVED')}
                      className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => handleStudioAction(studio.id, 'REJECTED')}
                      className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      Reject
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredStudios.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No studios found</h3>
          <p className="text-gray-600">
            {filter === 'ALL' 
              ? 'No studios have been registered yet.'
              : `No studios with ${filter.toLowerCase()} status.`
            }
          </p>
        </div>
      )}

      {/* Modals */}
      {showCreateModal && (
        <CreateStudioModal
          onClose={() => setShowCreateModal(false)}
          onStudioCreated={handleStudioCreated}
        />
      )}

      {showEditModal && selectedStudio && (
        <EditStudioModal
          studio={selectedStudio}
          onClose={() => {
            setShowEditModal(false);
            setSelectedStudio(null);
          }}
          onStudioUpdated={handleStudioUpdated}
        />
      )}

      {showViewModal && selectedStudio && (
        <ViewStudioModal
          studio={selectedStudio}
          onClose={() => {
            setShowViewModal(false);
            setSelectedStudio(null);
          }}
        />
      )}

      {showDeleteModal && selectedStudio && (
        <DeleteConfirmModal
          title="Delete Studio"
          message={`Are you sure you want to delete "${selectedStudio.name}"? This action cannot be undone.`}
          onConfirm={handleStudioDeleted}
          onCancel={() => {
            setShowDeleteModal(false);
            setSelectedStudio(null);
          }}
        />
      )}
    </div>
  );
}
